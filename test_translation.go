package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"time"
)

// 测试翻译接口的简单脚本
func main() {
	// 测试数据
	translationReq := map[string]interface{}{
		"service": "gpt-4o-mini",
		"text": map[string]string{
			"0": "Hello world",
			"1": "Nice, clean UI~",
			"2": "Welcome to our website",
		},
		"target_lang": "zh-CN",
	}

	// 序列化请求
	jsonData, err := json.Marshal(translationReq)
	if err != nil {
		fmt.Printf("Failed to marshal request: %v\n", err)
		return
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", "http://localhost:8080/translation", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("Failed to create request: %v\n", err)
		return
	}

	// 设置请求头（需要有效的JWT token）
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer your-jwt-token-here")
	req.Header.Set("X-Device-ID", "test-device")
	req.Header.Set("X-Timestamp", fmt.Sprintf("%d", time.Now().Unix()))

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("Failed to send request: %v\n", err)
		return
	}
	defer resp.Body.Close()

	// 读取响应
	var response map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		fmt.Printf("Failed to decode response: %v\n", err)
		return
	}

	// 打印响应
	fmt.Printf("Status: %s\n", resp.Status)
	fmt.Printf("Response: %+v\n", response)

	// 测试用量信息接口
	fmt.Println("\n--- Testing Usage Info API ---")
	testUsageInfo()
}

func testUsageInfo() {
	req, err := http.NewRequest("GET", "http://localhost:8080/usageInfo", nil)
	if err != nil {
		fmt.Printf("Failed to create usage info request: %v\n", err)
		return
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer your-jwt-token-here")
	req.Header.Set("X-Device-ID", "test-device")
	req.Header.Set("X-Timestamp", fmt.Sprintf("%d", time.Now().Unix()))

	// 发送请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("Failed to send usage info request: %v\n", err)
		return
	}
	defer resp.Body.Close()

	// 读取响应
	var response map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		fmt.Printf("Failed to decode usage info response: %v\n", err)
		return
	}

	// 打印响应
	fmt.Printf("Usage Info Status: %s\n", resp.Status)
	fmt.Printf("Usage Info Response: %+v\n", response)
}
