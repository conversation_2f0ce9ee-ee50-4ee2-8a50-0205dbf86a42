package service

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"github.com/bilangpage/bilangpage-api/internal/database"
	"github.com/bilangpage/bilangpage-api/internal/model"
	"time"
)

// UserService 用户服务
type UserService struct{}

// NewUserService 创建用户服务
func NewUserService() *UserService {
	return &UserService{}
}

// FindOrCreateUser 查找或创建用户
func (s *UserService) FindOrCreateUser(provider, providerID, username, nickname, avatar, email string) (*model.User, error) {
	var user model.User

	// 先查找是否存在该用户
	err := database.DB.Where("provider = ? AND provider_id = ?", provider, providerID).First(&user).Error
	if err == nil {
		// 用户存在，更新最后登录时间和其他信息（只更新非空值）
		user.LastLogin = time.Now()

		// 只在新值不为空时才更新字段，避免覆盖已有的有效数据
		if username != "" {
			user.Username = username
		}
		if nickname != "" {
			user.Nickname = nickname
		}
		if avatar != "" {
			user.Avatar = avatar
		}
		if email != "" {
			user.Email = email
		}

		if err := database.DB.Save(&user).Error; err != nil {
			return nil, fmt.Errorf("failed to update user: %v", err)
		}
		return &user, nil
	}

	// 用户不存在，创建新用户
	userID, err := s.generateUserID()
	if err != nil {
		return nil, fmt.Errorf("failed to generate user ID: %v", err)
	}

	now := time.Now()
	user = model.User{
		ID:         userID,
		Username:   username,
		Nickname:   nickname,
		Avatar:     avatar,
		Email:      email,
		Provider:   provider,
		ProviderID: providerID,
		CreatedAt:  now,
		LastLogin:  now,
	}

	if err := database.DB.Create(&user).Error; err != nil {
		return nil, fmt.Errorf("failed to create user: %v", err)
	}

	return &user, nil
}

// GetUserByID 根据ID获取用户
func (s *UserService) GetUserByID(userID string) (*model.User, error) {
	var user model.User
	err := database.DB.Where("id = ?", userID).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// generateUserID 生成用户ID
func (s *UserService) generateUserID() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}
