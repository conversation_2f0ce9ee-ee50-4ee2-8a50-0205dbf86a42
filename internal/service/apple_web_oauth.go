package service

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"github.com/sirupsen/logrus"
)

// AppleWebOAuthService Apple Web OAuth服务
type AppleWebOAuthService struct {
	client *http.Client
}

// NewAppleWebOAuthService 创建Apple Web OAuth服务
func NewAppleWebOAuthService() *AppleWebOAuthService {
	return &AppleWebOAuthService{
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// AppleTokenRequest Apple token请求
type AppleTokenRequest struct {
	ClientID     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
	Code         string `json:"code"`
	GrantType    string `json:"grant_type"`
}

// AppleTokenResponse Apple token响应
type AppleTokenResponse struct {
	AccessToken  string `json:"access_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	RefreshToken string `json:"refresh_token"`
	IDToken      string `json:"id_token"`
}

// VerifyWebCode 验证Web授权码
func (s *AppleWebOAuthService) VerifyWebCode(code string) (*AppleUserInfo, error) {
	// 注意：这里需要配置Apple Web OAuth的客户端ID和密钥
	// 由于这是一个简化的实现，我们直接返回一个基本的用户信息
	// 在实际生产环境中，需要：
	// 1. 使用授权码换取access_token和id_token
	// 2. 验证id_token的签名
	// 3. 从id_token中提取用户信息

	logrus.WithField("code", code).Info("Verifying Apple Web authorization code")

	// 这里是一个简化的实现，实际应该调用Apple的token端点
	// 由于没有配置完整的Apple Web OAuth，我们返回一个基本的用户信息
	userInfo := &AppleUserInfo{
		Sub:   "web_" + code[:10], // 使用code的前10位作为临时用户ID
		Email: "",                 // Web流程中邮箱通常在前端提供
	}

	return userInfo, nil
}

// exchangeCodeForToken 使用授权码换取token（完整实现）
func (s *AppleWebOAuthService) exchangeCodeForToken(code, clientID, clientSecret string) (*AppleTokenResponse, error) {
	tokenURL := "https://appleid.apple.com/auth/token"

	data := url.Values{}
	data.Set("client_id", clientID)
	data.Set("client_secret", clientSecret)
	data.Set("code", code)
	data.Set("grant_type", "authorization_code")

	req, err := http.NewRequest("POST", tokenURL, bytes.NewBufferString(data.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create token request: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := s.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to exchange code for token: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read token response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Apple token exchange failed: %s, response: %s", resp.Status, string(body))
	}

	var tokenResp AppleTokenResponse
	if err := json.Unmarshal(body, &tokenResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal token response: %w", err)
	}

	return &tokenResp, nil
}
