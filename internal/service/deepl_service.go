package service

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"
	"unicode/utf8"

	"github.com/bilangpage/bilangpage-api/internal/config"
	"github.com/bilangpage/bilangpage-api/internal/dto"
	"github.com/sirupsen/logrus"
)

// DeepLService DeepL翻译服务
type DeepLService struct {
	apiKey  string
	baseURL string
	client  *http.Client
}

// NewDeepLService 创建DeepL服务
func NewDeepLService() *DeepLService {
	return &DeepLService{
		apiKey:  config.C.Translation.DeepL.APIKey,
		baseURL: config.C.Translation.DeepL.BaseURL,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// DeepLRequest DeepL API请求
type DeepLRequest struct {
	Text       []string `json:"text"`
	TargetLang string   `json:"target_lang"`
	SourceLang string   `json:"source_lang,omitempty"`
}

// DeepLResponse DeepL API响应
type DeepLResponse struct {
	Translations []DeepLTranslation `json:"translations"`
}

// DeepLTranslation DeepL翻译结果
type DeepLTranslation struct {
	DetectedSourceLang string `json:"detected_source_language"`
	Text               string `json:"text"`
}

// TranslateConcurrent 并发翻译多个文本
func (s *DeepLService) TranslateConcurrent(texts map[string]string, targetLang string) (map[string]string, *dto.TranslationUsage, error) {
	if s.apiKey == "" {
		return nil, nil, fmt.Errorf("DeepL API key not configured")
	}

	results := make(map[string]string)
	var mu sync.Mutex
	var wg sync.WaitGroup
	errChan := make(chan error, len(texts))

	totalChars := 0
	for _, text := range texts {
		totalChars += utf8.RuneCountInString(text) // 使用Unicode字符数
	}

	// 并发翻译每个文本
	for key, text := range texts {
		wg.Add(1)
		go func(k, t string) {
			defer wg.Done()

			translated, err := s.translateSingle(t, targetLang)
			if err != nil {
				errChan <- fmt.Errorf("failed to translate text %s: %w", k, err)
				return
			}

			mu.Lock()
			results[k] = translated
			mu.Unlock()
		}(key, text)
	}

	wg.Wait()
	close(errChan)

	// 检查是否有错误
	if len(errChan) > 0 {
		return nil, nil, <-errChan
	}

	usage := &dto.TranslationUsage{
		CharCount: totalChars,
	}

	return results, usage, nil
}

// translateSingle 翻译单个文本
func (s *DeepLService) translateSingle(text, targetLang string) (string, error) {
	deepLLang := dto.GetDeepLLanguageCode(targetLang)

	reqData := DeepLRequest{
		Text:       []string{text},
		TargetLang: deepLLang,
	}

	jsonData, err := json.Marshal(reqData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequest("POST", s.baseURL+"/v2/translate", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "DeepL-Auth-Key "+s.apiKey)
	req.Header.Set("Content-Type", "application/json")

	resp, err := s.client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("DeepL API error: %s, response: %s", resp.Status, string(body))
	}

	var deepLResp DeepLResponse
	if err := json.Unmarshal(body, &deepLResp); err != nil {
		return "", fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if len(deepLResp.Translations) == 0 {
		return "", fmt.Errorf("no translations returned")
	}

	return deepLResp.Translations[0].Text, nil
}

// TranslateBatch 批量翻译（DeepL支持一次请求多个文本）
func (s *DeepLService) TranslateBatch(texts map[string]string, targetLang string) (map[string]string, *dto.TranslationUsage, error) {
	if s.apiKey == "" {
		return nil, nil, fmt.Errorf("DeepL API key not configured")
	}

	// 准备文本数组和索引映射
	textArray := make([]string, 0, len(texts))
	keyArray := make([]string, 0, len(texts))

	totalChars := 0
	for key, text := range texts {
		textArray = append(textArray, text)
		keyArray = append(keyArray, key)
		totalChars += utf8.RuneCountInString(text) // 使用Unicode字符数
	}

	deepLLang := dto.GetDeepLLanguageCode(targetLang)

	reqData := DeepLRequest{
		Text:       textArray,
		TargetLang: deepLLang,
	}

	jsonData, err := json.Marshal(reqData)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequest("POST", s.baseURL+"/v2/translate", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "DeepL-Auth-Key "+s.apiKey)
	req.Header.Set("Content-Type", "application/json")

	resp, err := s.client.Do(req)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		logrus.WithFields(logrus.Fields{
			"status": resp.Status,
			"body":   string(body),
		}).Error("DeepL API error")
		return nil, nil, fmt.Errorf("DeepL API error: %s", resp.Status)
	}

	var deepLResp DeepLResponse
	if err := json.Unmarshal(body, &deepLResp); err != nil {
		return nil, nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if len(deepLResp.Translations) != len(textArray) {
		return nil, nil, fmt.Errorf("translation count mismatch: expected %d, got %d", len(textArray), len(deepLResp.Translations))
	}

	// 构建结果映射
	results := make(map[string]string)
	for i, translation := range deepLResp.Translations {
		results[keyArray[i]] = translation.Text
	}

	usage := &dto.TranslationUsage{
		CharCount: totalChars,
	}

	return results, usage, nil
}
