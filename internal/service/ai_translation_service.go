package service

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strings"
	"time"
	"unicode/utf8"

	"github.com/bilangpage/bilangpage-api/internal/config"
	"github.com/bilangpage/bilangpage-api/internal/dto"
	"github.com/sirupsen/logrus"
)

// AITranslationService AI翻译服务（支持DeepSeek和OpenRouter）
type AITranslationService struct {
	client *http.Client
}

// NewAITranslationService 创建AI翻译服务
func NewAITranslationService() *AITranslationService {
	return &AITranslationService{
		client: &http.Client{
			Timeout: 60 * time.Second,
		},
	}
}

// ChatCompletionRequest OpenAI格式的请求
type ChatCompletionRequest struct {
	Model       string    `json:"model"`
	Messages    []Message `json:"messages"`
	Temperature float64   `json:"temperature,omitempty"`
}

// Message 消息
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// ChatCompletionResponse OpenAI格式的响应
type ChatCompletionResponse struct {
	ID      string   `json:"id"`
	Object  string   `json:"object"`
	Created int64    `json:"created"`
	Model   string   `json:"model"`
	Choices []Choice `json:"choices"`
}

// Choice 选择
type Choice struct {
	Index        int     `json:"index"`
	Message      Message `json:"message"`
	FinishReason string  `json:"finish_reason"`
}

// TranslateWithAI 使用AI服务翻译（合并请求）
func (s *AITranslationService) TranslateWithAI(texts map[string]string, targetLang string, serviceType dto.TranslationServiceType) (map[string]string, *dto.TranslationUsage, error) {
	if len(texts) == 0 {
		return make(map[string]string), &dto.TranslationUsage{CharCount: 0}, nil
	}

	// 获取API配置
	var apiKey, baseURL, model string
	switch serviceType {
	case dto.ServiceDeepSeekV3:
		apiKey = config.C.Translation.DeepSeek.APIKey
		baseURL = config.C.Translation.DeepSeek.BaseURL
		model = config.C.Translation.DeepSeek.Model
	case dto.ServiceGPT4OMini:
		apiKey = config.C.Translation.OpenRouter.APIKey
		baseURL = config.C.Translation.OpenRouter.BaseURL
		model = config.C.Translation.OpenRouter.Model
	default:
		return nil, nil, fmt.Errorf("unsupported AI service type: %s", serviceType)
	}

	if apiKey == "" {
		return nil, nil, fmt.Errorf("API key not configured for service: %s", serviceType)
	}

	// 尝试批量翻译
	result, err := s.translateBatch(texts, targetLang, apiKey, baseURL, model)
	if err != nil {
		logrus.WithError(err).Warn("Batch translation failed, falling back to individual translations")
		return nil, nil, err
	}

	return result, s.calculateUsage(texts), nil
}

// translateBatch 批量翻译
func (s *AITranslationService) translateBatch(texts map[string]string, targetLang string, apiKey, baseURL, model string) (map[string]string, error) {
	const separator = "|||SEPARATOR|||"

	// 构建合并文本
	var combinedText strings.Builder
	i := 0
	for key, text := range texts {
		combinedText.WriteString(fmt.Sprintf("[%s] %s", key, text))
		if i < len(texts)-1 {
			combinedText.WriteString(separator)
		}
		i++
	}

	// 构建批量翻译的提示词
	langName := s.getLanguageName(targetLang)
	messages := []Message{
		{
			Role:    "system",
			Content: fmt.Sprintf(`你是一个专业的翻译助手。我会给你多个用"%s"分隔的文本，每个文本前面都有key标识如[key1], [key2]等。请按照相同的格式翻译每个文本，保持key标识和分隔符不变。直接返回翻译结果，不要添加任何解释。`, separator),
		},
		{
			Role:    "user",
			Content: fmt.Sprintf("将以下%d个文本翻译成%s，保持key标识和分隔符格式：\n%s", len(texts), langName, combinedText.String()),
		},
	}

	// 调用API
	content, err := s.callAPI(messages, apiKey, baseURL, model)
	if err != nil {
		return nil, err
	}

	// 解析批量结果
	return s.distributeBatchResults(content, texts, separator)
}

// callAPI 调用AI API
func (s *AITranslationService) callAPI(messages []Message, apiKey, baseURL, model string) (string, error) {
	reqData := ChatCompletionRequest{
		Model:       model,
		Messages:    messages,
		Temperature: 0.3,
	}

	jsonData, err := json.Marshal(reqData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequest("POST", baseURL+"/chat/completions", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", "application/json")

	// OpenRouter需要额外的头部
	req.Header.Set("HTTP-Referer", "https://github.com/wujiuye/bilangpage")
	req.Header.Set("X-Title", "BiLangPage Pro")
	
	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		var errorMsg string
		if resp.StatusCode == 403 {
			errorMsg = fmt.Sprintf("API 403 Forbidden: Access denied. Check API key, model permissions, or account balance.\nModel: %s", model)
		} else {
			errorMsg = fmt.Sprintf("API request failed (%d): %s", resp.StatusCode, string(body))
		}
		return "", fmt.Errorf(errorMsg)
	}

	var aiResp ChatCompletionResponse
	if err := json.Unmarshal(body, &aiResp); err != nil {
		return "", fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if len(aiResp.Choices) == 0 {
		return "", fmt.Errorf("no translation choices returned")
	}

	return strings.TrimSpace(aiResp.Choices[0].Message.Content), nil
}

// distributeBatchResults 分发批量翻译结果（简化版本）
func (s *AITranslationService) distributeBatchResults(batchResult string, originalTexts map[string]string, separator string) (map[string]string, error) {
	results := make(map[string]string)

	// 首先尝试按分隔符分割
	splitResults := strings.Split(batchResult, separator)

	// 遍历分割后的结果，直接解析每个部分的key
	for _, part := range splitResults {
		part = strings.TrimSpace(part)
		if part == "" {
			continue
		}

		// 使用正则表达式匹配 [key] content 格式
		re := regexp.MustCompile(`^\[(.*?)\]\s*(.*)$`)
		match := re.FindStringSubmatch(part)

		if len(match) == 3 {
			key := strings.TrimSpace(match[1])
			content := strings.TrimSpace(match[2])

			// 检查key是否在原始文本中存在
			if _, exists := originalTexts[key]; exists {
				results[key] = content
			}
		}
	}

	// 如果分隔符分割没有得到完整结果，尝试按行分割
	if len(results) != len(originalTexts) {
		results = make(map[string]string) // 重置结果
		lines := strings.Split(batchResult, "\n")

		for _, line := range lines {
			line = strings.TrimSpace(line)
			if line == "" {
				continue
			}

			// 使用正则表达式匹配 [key] content 格式
			re := regexp.MustCompile(`^\[(.*?)\]\s*(.*)$`)
			match := re.FindStringSubmatch(line)

			if len(match) == 3 {
				key := strings.TrimSpace(match[1])
				content := strings.TrimSpace(match[2])

				// 检查key是否在原始文本中存在
				if _, exists := originalTexts[key]; exists {
					results[key] = content
				}
			}
		}
	}

	// 检查是否所有key都有对应的翻译结果
	if len(results) == len(originalTexts) {
		return results, nil
	}

	return nil, fmt.Errorf("failed to parse batch translation results: got %d results, expected %d", len(results), len(originalTexts))
}

// calculateUsage 计算用量
func (s *AITranslationService) calculateUsage(texts map[string]string) *dto.TranslationUsage {
	totalChars := 0
	for _, text := range texts {
		totalChars += utf8.RuneCountInString(text)
	}

	return &dto.TranslationUsage{
		CharCount: totalChars,
	}
}

// getLanguageName 获取语言名称
func (s *AITranslationService) getLanguageName(langCode string) string {
	switch langCode {
	case "zh-CN":
		return "简体中文"
	case "en":
		return "英文"
	case "ja":
		return "日文"
	case "ko":
		return "韩文"
	case "ar":
		return "阿拉伯文"
	case "de":
		return "德文"
	case "fr":
		return "法文"
	default:
		return "英文"
	}
}
