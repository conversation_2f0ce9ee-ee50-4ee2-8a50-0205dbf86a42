package service

import (
	"crypto/rsa"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/golang-jwt/jwt/v5"
	"io"
	"math/big"
	"net/http"
	"time"

	"github.com/bilangpage/bilangpage-api/internal/dto"
	"github.com/sirupsen/logrus"
)

// AppleOAuthService Apple OAuth服务
type AppleOAuthService struct {
}

// AppleJWTClaims Apple JWT Claims
type AppleJWTClaims struct {
	Iss           string      `json:"iss"`                 // 签发者，应该是 https://appleid.apple.com
	Aud           string      `json:"aud"`                 // 受众，应该是我们的 client_id
	Exp           int64       `json:"exp"`                 // 过期时间
	Iat           int64       `json:"iat"`                 // 签发时间
	Sub           string      `json:"sub"`                 // 用户的唯一标识符
	Email         string      `json:"email"`               // 用户邮箱
	EmailVerified interface{} `json:"email_verified"`      // 邮箱验证状态（可能是bool或string）
	AuthTime      int64       `json:"auth_time,omitempty"` // 认证时间
	Nonce         string      `json:"nonce,omitempty"`     // 随机值
	jwt.RegisteredClaims
}

// ApplePublicKey Apple公钥结构体
type ApplePublicKey struct {
	Kty string `json:"kty"`
	Kid string `json:"kid"`
	Use string `json:"use"`
	Alg string `json:"alg"`
	N   string `json:"n"`
	E   string `json:"e"`
}

// ApplePublicKeys Apple公钥集合结构体
type ApplePublicKeys struct {
	Keys []ApplePublicKey `json:"keys"`
}

// AppleUserInfo Apple用户信息
type AppleUserInfo struct {
	Sub   string `json:"sub"`
	Email string `json:"email"`
	Name  struct {
		FirstName string `json:"firstName"`
		LastName  string `json:"lastName"`
	} `json:"name,omitempty"`
}

// NewAppleOAuthService 创建Apple OAuth服务
func NewAppleOAuthService() *AppleOAuthService {
	return &AppleOAuthService{}
}

// VerifyAppleCredentials 验证Apple登录凭据
func (s *AppleOAuthService) VerifyAppleCredentials(req dto.AppleLoginRequest) (*AppleUserInfo, error) {
	if req.IdentityToken == "" {
		return nil, errors.New("identity_token is required for Apple Sign In")
	}
	userInfo, err := s.validateIDToken(req.IdentityToken)
	if err != nil {
		logrus.Errorf("ID Token验证失败: %v", err)
		return nil, fmt.Errorf("ID Token validation failed: %w", err)
	}

	// 处理额外的用户信息（首次登录时才有）
	if req.User != "" {
		if err := s.parseAdditionalUserInfo(req.User, userInfo); err != nil {
			logrus.Warnf("解析额外用户信息失败: %v", err)
		}
	}

	logrus.Infof("Apple登录验证成功, 用户ID: %s, 邮箱: %s, 姓名: %s %s",
		userInfo.Sub, userInfo.Email, userInfo.Name.FirstName, userInfo.Name.LastName)
	return userInfo, nil
}

// validateIDToken 验证ID Token
func (s *AppleOAuthService) validateIDToken(idToken string) (*AppleUserInfo, error) {
	// 解析JWT token（不验证签名，先获取基本信息）
	token, err := jwt.ParseWithClaims(idToken, &AppleJWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// 获取Apple的公钥进行验证
		return s.getApplePublicKey(token)
	})
	if err != nil {
		return nil, fmt.Errorf("failed to parse JWT: %w", err)
	}
	claims, ok := token.Claims.(*AppleJWTClaims)
	if !ok {
		return nil, errors.New("invalid JWT claims")
	}
	// 验证基本字段
	if err := s.validateClaims(claims); err != nil {
		return nil, err
	}
	// 转换为用户信息
	userInfo := &AppleUserInfo{
		Sub:   claims.Sub,
		Email: claims.Email,
	}
	return userInfo, nil
}

// validateClaims 验证JWT claims
func (s *AppleOAuthService) validateClaims(claims *AppleJWTClaims) error {
	now := time.Now().Unix()
	// 验证签发者
	if claims.Iss != "https://appleid.apple.com" {
		return fmt.Errorf("invalid issuer: %s", claims.Iss)
	}
	// 验证受众
	if claims.Aud != "com.wujiuye.BiLangePagePro" {
		return fmt.Errorf("invalid audience: %s", claims.Aud)
	}
	// 验证过期时间
	if claims.Exp < now {
		return fmt.Errorf("token expired at %d, now is %d", claims.Exp, now)
	}
	// 验证签发时间（不能是未来时间）
	if claims.Iat > now+300 { // 允许5分钟的时钟偏差
		return fmt.Errorf("token issued in future: %d, now is %d", claims.Iat, now)
	}
	// 验证用户ID
	if claims.Sub == "" {
		return errors.New("missing subject (user ID)")
	}
	return nil
}

// getApplePublicKey 获取Apple的公钥
func (s *AppleOAuthService) getApplePublicKey(token *jwt.Token) (interface{}, error) {
	// 检查签名算法
	if token.Method.Alg() != "RS256" {
		return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
	}
	// 获取密钥ID
	kid, ok := token.Header["kid"].(string)
	if !ok {
		return nil, errors.New("kid not found in token header")
	}
	// 从Apple获取公钥
	resp, err := http.Get("https://appleid.apple.com/auth/keys")
	if err != nil {
		return nil, fmt.Errorf("failed to fetch Apple public keys: %w", err)
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read Apple public keys response: %w", err)
	}
	var keys ApplePublicKeys
	if err := json.Unmarshal(body, &keys); err != nil {
		return nil, fmt.Errorf("failed to parse Apple public keys: %w", err)
	}
	// 查找匹配的密钥
	for _, key := range keys.Keys {
		if key.Kid == kid {
			return s.createRSAPublicKey(key)
		}
	}
	return nil, fmt.Errorf("public key not found for kid: %s", kid)
}

// createRSAPublicKey 从Apple公钥创建RSA公钥
func (s *AppleOAuthService) createRSAPublicKey(key ApplePublicKey) (*rsa.PublicKey, error) {
	nBytes, err := base64.RawURLEncoding.DecodeString(key.N)
	if err != nil {
		return nil, fmt.Errorf("failed to decode n: %w", err)
	}
	eBytes, err := base64.RawURLEncoding.DecodeString(key.E)
	if err != nil {
		return nil, fmt.Errorf("failed to decode e: %w", err)
	}
	n := new(big.Int).SetBytes(nBytes)
	var e int
	if len(eBytes) <= 4 {
		eInt := 0
		for _, b := range eBytes {
			eInt = eInt<<8 | int(b)
		}
		e = eInt
	} else {
		return nil, errors.New("exponent too large")
	}
	return &rsa.PublicKey{
		N: n,
		E: e,
	}, nil
}

// parseAdditionalUserInfo 解析额外的用户信息（首次登录时才有）
func (s *AppleOAuthService) parseAdditionalUserInfo(userStr string, userInfo *AppleUserInfo) error {
	var userData map[string]interface{}
	if err := json.Unmarshal([]byte(userStr), &userData); err != nil {
		return fmt.Errorf("failed to parse user data: %w", err)
	}
	// 解析姓名信息
	if name, ok := userData["name"].(map[string]interface{}); ok {
		if firstName, ok := name["firstName"].(string); ok {
			userInfo.Name.FirstName = firstName
		}
		if lastName, ok := name["lastName"].(string); ok {
			userInfo.Name.LastName = lastName
		}
	}
	return nil
}
