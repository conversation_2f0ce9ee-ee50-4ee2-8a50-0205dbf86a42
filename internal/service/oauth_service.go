package service

import (
	"github.com/bilangpage/bilangpage-api/internal/dto"
)

// OAuthService OAuth主服务，统一管理各种OAuth提供商
type OAuthService struct {
	apple *AppleOAuthService
}

// NewOAuthService 创建OAuth服务
func NewOAuthService() *OAuthService {
	return &OAuthService{
		apple: NewAppleOAuthService(),
	}
}

// VerifyAppleCredentials 验证Apple凭据
func (s *OAuthService) VerifyAppleCredentials(req dto.AppleLoginRequest) (*AppleUserInfo, error) {
	return s.apple.VerifyAppleCredentials(req)
}
