package service

import (
	"fmt"
	"sync"
	"time"

	"github.com/bilangpage/bilangpage-api/internal/dto"
)

// CacheItem 缓存项
type CacheItem struct {
	Data      interface{}
	ExpiresAt time.Time
}

// IsExpired 检查是否过期
func (c *CacheItem) IsExpired() bool {
	return time.Now().After(c.ExpiresAt)
}

// MemoryCache 内存缓存
type MemoryCache struct {
	items map[string]*CacheItem
	mutex sync.RWMutex
}

// NewMemoryCache 创建内存缓存
func NewMemoryCache() *MemoryCache {
	cache := &MemoryCache{
		items: make(map[string]*CacheItem),
	}

	// 启动清理过期项的goroutine
	go cache.cleanupExpiredItems()

	return cache
}

// Set 设置缓存项
func (m *MemoryCache) Set(key string, value interface{}, ttl time.Duration) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.items[key] = &CacheItem{
		Data:      value,
		ExpiresAt: time.Now().Add(ttl),
	}
}

// Get 获取缓存项
func (m *MemoryCache) Get(key string) (interface{}, bool) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	item, exists := m.items[key]
	if !exists || item.IsExpired() {
		return nil, false
	}

	return item.Data, true
}

// Delete 删除缓存项
func (m *MemoryCache) Delete(key string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	delete(m.items, key)
}

// cleanupExpiredItems 清理过期项
func (m *MemoryCache) cleanupExpiredItems() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		m.mutex.Lock()
		for key, item := range m.items {
			if item.IsExpired() {
				delete(m.items, key)
			}
		}
		m.mutex.Unlock()
	}
}

// CacheService 缓存服务
type CacheService struct {
	memoryCache *MemoryCache
}

// NewCacheService 创建缓存服务
func NewCacheService() *CacheService {
	return &CacheService{
		memoryCache: NewMemoryCache(),
	}
}

const (
	// 缓存过期时间（5分钟）
	UsageInfoCacheTTL = 5 * time.Minute
	SubscriptionCacheTTL = 5 * time.Minute
)

// GetUsageInfoFromCache 从缓存获取用量信息
func (s *CacheService) GetUsageInfoFromCache(userID string) (*dto.UsageInfoResponse, error) {
	key := "usage_info:" + userID

	if data, exists := s.memoryCache.Get(key); exists {
		if usageInfo, ok := data.(*dto.UsageInfoResponse); ok {
			return usageInfo, nil
		}
	}

	return nil, fmt.Errorf("usage info not found in cache")
}

// SetUsageInfoToCache 设置用量信息到缓存
func (s *CacheService) SetUsageInfoToCache(userID string, usageInfo *dto.UsageInfoResponse) error {
	key := "usage_info:" + userID
	s.memoryCache.Set(key, usageInfo, UsageInfoCacheTTL)
	return nil
}

// InvalidateUsageInfoCache 使用量信息缓存失效
func (s *CacheService) InvalidateUsageInfoCache(userID string) error {
	key := "usage_info:" + userID
	s.memoryCache.Delete(key)
	return nil
}

// GetSubscriptionFromCache 从缓存获取订阅信息
func (s *CacheService) GetSubscriptionFromCache(userID string) (*dto.SubscriptionStatusResponse, error) {
	key := "subscription:" + userID

	if data, exists := s.memoryCache.Get(key); exists {
		if subscription, ok := data.(*dto.SubscriptionStatusResponse); ok {
			return subscription, nil
		}
	}

	return nil, fmt.Errorf("subscription not found in cache")
}

// SetSubscriptionToCache 设置订阅信息到缓存
func (s *CacheService) SetSubscriptionToCache(userID string, subscription *dto.SubscriptionStatusResponse) error {
	key := "subscription:" + userID
	s.memoryCache.Set(key, subscription, SubscriptionCacheTTL)
	return nil
}

// InvalidateSubscriptionCache 订阅信息缓存失效
func (s *CacheService) InvalidateSubscriptionCache(userID string) error {
	key := "subscription:" + userID
	s.memoryCache.Delete(key)
	return nil
}

// GetCacheStats 获取缓存统计信息（用于监控）
func (s *CacheService) GetCacheStats() map[string]interface{} {
	s.memoryCache.mutex.RLock()
	itemCount := len(s.memoryCache.items)
	s.memoryCache.mutex.RUnlock()

	return map[string]interface{}{
		"cache_type":  "memory",
		"item_count":  itemCount,
		"available":   true,
	}
}
