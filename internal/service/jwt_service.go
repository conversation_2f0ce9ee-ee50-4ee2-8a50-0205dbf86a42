package service

import (
	"errors"
	"github.com/bilangpage/bilangpage-api/internal/config"
	"github.com/golang-jwt/jwt/v5"
	"time"
)

// JWTClaims JWT声明
type JWTClaims struct {
	UserID   string `json:"sub"`
	Username string `json:"username"`
	Provider string `json:"provider"`
	jwt.RegisteredClaims
}

// JWTService JWT服务
type JWTService struct {
	secret []byte
	issuer string
}

// NewJWTService 创建JWT服务
func NewJWTService() *JWTService {
	return &JWTService{
		secret: []byte(config.C.JWT.Secret),
		issuer: config.C.JWT.Issuer,
	}
}

// GenerateToken 生成JWT token
func (j *JWTService) GenerateToken(userID, username, provider string, app bool) (string, error) {
	now := time.Now()
	var expirationTime time.Time
	if app {
		expirationTime = now.Add(365 * 24 * time.Hour) // 365天
	} else {
		expirationTime = now.Add(7 * 24 * time.Hour) // 7天
	}

	claims := &JWTClaims{
		UserID:   userID,
		Username: username,
		Provider: provider,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    j.issuer,
			Subject:   userID,
			Audience:  []string{"BiLangPage"},
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(now),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(j.secret)
}

// ValidateToken 验证JWT token
func (j *JWTService) ValidateToken(tokenString string) (*JWTClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// 确保token的签名方法是我们期望的
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("unexpected signing method")
		}
		return j.secret, nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("invalid token")
}

// GetExpiresIn 获取token过期时间（秒）
func (j *JWTService) GetExpiresIn(app bool) int64 {
	if app {
		return 365 * 24 * 60 * 60 // 365天
	} else {
		return 7 * 24 * 60 * 60
	}
}
