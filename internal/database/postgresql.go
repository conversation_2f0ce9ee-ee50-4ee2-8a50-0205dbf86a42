package database

import (
	"fmt"
	"github.com/bilangpage/bilangpage-api/internal/config"
	"github.com/bilangpage/bilangpage-api/pkg/util"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"time"
)

var DB *gorm.DB

func init() {
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%d sslmode=require",
		config.C.PostgreSQL.Host,
		config.C.PostgreSQL.Username,
		config.C.PostgreSQL.Password,
		config.C.PostgreSQL.Database,
		config.C.PostgreSQL.Port)

	var err error
	DB, err = gorm.Open(postgres.Open(dsn), &gorm.Config{
		DisableForeignKeyConstraintWhenMigrating: true,
	})
	if err != nil {
		util.AbnormalExit(err)
	}

	sqlDB, err := DB.DB()
	if err != nil {
		util.AbnormalExit(err)
	}

	// Set connection pool settings
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)
}
