package database

import (
	"context"
	"fmt"
	"time"

	"github.com/bilangpage/bilangpage-api/internal/config"
	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"
)

var RedisClient *redis.Client

func init() {
	initRedis()
}

func initRedis() {
	RedisClient = redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", config.C.Redis.Host, config.C.Redis.Port),
		Password: config.C.Redis.Password,
		DB:       config.C.Redis.DB,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := RedisClient.Ping(ctx).Result()
	if err != nil {
		logrus.WithError(err).Warn("Failed to connect to Redis, cache will be disabled")
		RedisClient = nil
	} else {
		logrus.Info("Redis connected successfully")
	}
}
