package dto

import (
	"fmt"
	"github.com/bilangpage/bilangpage-api/internal/model"
)

// AppleLoginRequest Apple登录请求
type AppleLoginRequest struct {
	IdentityToken     string `json:"identity_token"`     // Apple Identity Token（移动应用直接提供）
	AuthorizationCode string `json:"authorization_code"` // Apple Authorization Code（Web应用使用）
	UserIdentifier    string `json:"user_identifier"`    // Apple User Identifier（可选，仅作为辅助信息）
	User              string `json:"user"`               // Apple用户信息（首次登录时包含姓名等信息）
}

// Validate 验证Apple登录请求参数
func (req *AppleLoginRequest) Validate() error {
	if req.IdentityToken == "" && req.AuthorizationCode == "" {
		return fmt.Errorf("either identity_token or authorization_code is required for Apple Sign In")
	}
	return nil
}

// LoginResponse 登录响应数据
type LoginResponse struct {
	Token     string    `json:"token"`      // JWT Token，有效期365天
	ExpiresIn int64     `json:"expires_in"` // 过期时间（秒）365*24*3600
	User      *UserInfo `json:"user"`       // 用户信息
}

// UserInfo 用户信息
type UserInfo struct {
	ID        string `json:"id"`                   // 用户唯一ID
	Username  string `json:"username"`             // 用户名
	Nickname  string `json:"nickname"`             // 显示昵称
	Avatar    string `json:"avatar"`               // 头像URL
	Email     string `json:"email"`                // 邮箱
	Provider  string `json:"provider"`             // 登录方式
	CreatedAt string `json:"created_at,omitempty"` // 创建时间（可选）
	LastLogin string `json:"last_login,omitempty"` // 最后登录时间（可选）
}

// ConvertUserToUserInfo 将User模型转换为UserInfo DTO
func ConvertUserToUserInfo(u *model.User, withTimestamp bool) *UserInfo {
	userInfo := &UserInfo{
		ID:       u.ID,
		Username: u.Username,
		Nickname: u.Nickname,
		Avatar:   u.Avatar,
		Email:    u.Email,
		Provider: u.Provider,
	}

	if withTimestamp {
		userInfo.CreatedAt = u.CreatedAt.Format("2006-01-02T15:04:05Z")
		userInfo.LastLogin = u.LastLogin.Format("2006-01-02T15:04:05Z")
	}

	return userInfo
}
