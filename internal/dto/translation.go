package dto

import (
	"fmt"
	"unicode/utf8"
)

// TranslationRequest 翻译请求
type TranslationRequest struct {
	Service    string            `json:"service" binding:"required"`     // 翻译服务类型
	Text       map[string]string `json:"text" binding:"required"`        // 要翻译的文本，key为序号，value为文本内容
	TargetLang string            `json:"target_lang" binding:"required"` // 目标语言代码
}

// Validate 验证翻译请求参数
func (req *TranslationRequest) Validate() error {
	if req.Service == "" {
		return fmt.Errorf("service is required")
	}
	if len(req.Text) == 0 {
		return fmt.Errorf("text is required and cannot be empty")
	}
	if req.TargetLang == "" {
		return fmt.Errorf("target_lang is required")
	}
	return nil
}

// GetTotalCharCount 获取总字符数（用于用量统计）
func (req *TranslationRequest) GetTotalCharCount() int {
	totalLength := 0
	for _, text := range req.Text {
		totalLength += utf8.RuneCountInString(text)
	}
	return totalLength
}

// TranslationResponse 翻译响应
type TranslationResponse struct {
	Text map[string]string `json:"text"` // 翻译结果，格式与请求中的text相同
}

// UsageInfoResponse 用量信息响应
type UsageInfoResponse struct {
	Limit         int   `json:"limit"`           // 总用量限制
	Usage         int   `json:"usage"`           // 已使用量
	Remaining     int   `json:"remaining"`       // 剩余可用量
	NextResetTime int64 `json:"next_reset_time"` // 下次重置时间戳（毫秒）
}

// TranslationServiceType 翻译服务类型
type TranslationServiceType string

const (
	ServiceDeepL      TranslationServiceType = "deepl"
	ServiceDeepSeekV3 TranslationServiceType = "deepseek-v3"
	ServiceGPT4OMini  TranslationServiceType = "gpt-4o-mini"
)

// IsAIService 判断是否为AI服务（需要合并请求）
func (t TranslationServiceType) IsAIService() bool {
	return t == ServiceDeepSeekV3 || t == ServiceGPT4OMini
}

// IsDeepLService 判断是否为DeepL服务（需要并发调用）
func (t TranslationServiceType) IsDeepLService() bool {
	return t == ServiceDeepL
}

// GetActualService 获取实际的服务类型
func (req *TranslationRequest) GetActualService() TranslationServiceType {
	switch req.Service {
	case "deepl":
		return ServiceDeepL
	case "deepseek-v3":
		return ServiceDeepSeekV3
	case "gpt-4o-mini":
		return ServiceGPT4OMini
	default:
		// 默认使用OpenRouter
		return ServiceGPT4OMini
	}
}

// GetDeepLLanguageCode 获取DeepL的语言代码
func GetDeepLLanguageCode(langCode string) string {
	switch langCode {
	case "zh-CN":
		return "ZH"
	case "en":
		return "EN"
	case "ja":
		return "JA"
	case "ko":
		return "KO"
	case "ar":
		return "AR"
	case "de":
		return "DE"
	case "fr":
		return "FR"
	default:
		return "EN"
	}
}

// TranslationUsage 翻译用量统计
type TranslationUsage struct {
	CharCount int `json:"char_count"` // 字符数量（统一使用字符数计算用量）
}

// GetUsageCount 获取用量计数（统一使用字符数）
func (u *TranslationUsage) GetUsageCount() int {
	return u.CharCount
}
