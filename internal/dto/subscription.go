package dto

import (
	"fmt"
	"time"
)

// SubscriptionSyncRequest 订阅同步请求
type SubscriptionSyncRequest struct {
	TransactionID         string `json:"transactionId" binding:"required"`
	OriginalTransactionID string `json:"originalTransactionId" binding:"required"`
	ProductID             string `json:"productId" binding:"required"`
	ExpirationDate             *int64 `json:"expirationDate" binding:"omitempty"`
	Platform              string `json:"platform" binding:"required"`
	App                   string `json:"app" binding:"required"`
}

// Validate 验证请求参数
func (r *SubscriptionSyncRequest) Validate() error {
	if r.TransactionID == "" {
		return fmt.Errorf("transactionId is required")
	}
	if r.OriginalTransactionID == "" {
		return fmt.Errorf("originalTransactionId is required")
	}
	if r.ProductID == "" {
		return fmt.Errorf("productId is required")
	}
	if r.Platform != "macOS" {
		return fmt.Errorf("platform must be 'macOS'")
	}
	if r.App == "" {
		return fmt.Errorf("app is required")
	}
	return nil
}

// SubscriptionSyncResponse 订阅同步响应
type SubscriptionSyncResponse struct {
	Subscription       *SubscriptionInfo `json:"subscription"`
	VerificationStatus string            `json:"verificationStatus"`
}

// SubscriptionInfo 订阅信息
type SubscriptionInfo struct {
	ProductID     string `json:"productId"`
	ExpiresAt     int64  `json:"expiresAt"`     // Unix timestamp in milliseconds
	IsValid       bool   `json:"isValid"`
	TransactionID string `json:"transactionId,omitempty"`
}

// SubscriptionStatusResponse 订阅状态响应
type SubscriptionStatusResponse struct {
	ProductID string `json:"productId"`
	ExpiresAt int64  `json:"expiresAt"` // Unix timestamp in milliseconds
	IsValid   bool   `json:"isValid"`
}

// AppleTransactionInfo Apple交易详细信息（StoreKit 2）
type AppleTransactionInfo struct {
	ProductID             string `json:"productId"`
	TransactionID         string `json:"transactionId"`
	OriginalTransactionID string `json:"originalTransactionId"`
	BundleID              string `json:"bundleId"`
	ExpiresDate           int64  `json:"expiresDate"`       // Unix timestamp in milliseconds
	PurchaseDate          int64  `json:"purchaseDate"`      // Unix timestamp in milliseconds
	Type                  string `json:"type"`              // "Auto-Renewable Subscription"
	InAppOwnershipType    string `json:"inAppOwnershipType"` // "PURCHASED"
	SignedDate            int64  `json:"signedDate"`        // Unix timestamp in milliseconds
}

// ConvertToSubscriptionInfo 转换为订阅信息
func (t *AppleTransactionInfo) ConvertToSubscriptionInfo() *SubscriptionInfo {
	return &SubscriptionInfo{
		ProductID:     t.ProductID,
		ExpiresAt:     t.ExpiresDate,
		IsValid:       t.ExpiresDate > time.Now().UnixMilli(),
		TransactionID: t.TransactionID,
	}
} 