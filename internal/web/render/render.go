package render

import (
	"github.com/bilangpage/bilangpage-api/internal/dto"
	"github.com/gin-gonic/gin"
)

// ResponseSuccess 成功响应
func ResponseSuccess(c *gin.Context, data interface{}) {
	response := dto.NewSuccessResponse(data)
	c.<PERSON>(200, response)
}

// ResponseError 错误响应
func ResponseError(c *gin.Context, httpStatus int, code int, message string) {
	response := dto.NewErrorResponse(code, message)
	c.JSON(httpStatus, response)
}

// ResponseBadRequest 400错误响应
func ResponseBadRequest(c *gin.Context, message string) {
	ResponseError(c, 400, 400, message)
}

// ResponseUnauthorized 401错误响应
func ResponseUnauthorized(c *gin.Context, message string) {
	ResponseError(c, 401, 401, message)
}

// ResponseInternalServerError 500错误响应
func ResponseInternalServerError(c *gin.Context, message string) {
	ResponseError(c, 500, 500, message)
}

// Response 兼容旧版本的响应
func Response(c *gin.Context, data interface{}) {
	ResponseSuccess(c, data)
}
