package filter

import (
	"fmt"
	"github.com/bilangpage/bilangpage-api/pkg/util"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"net"
	"net/http"
	"strings"
	"time"
)

func getClientIp(req *http.Request) string {
	xff := req.Header.Get("X-Forwarded-For")
	ip := strings.TrimSpace(strings.Split(xff, ",")[0])
	if ip != "" {
		return ip
	}
	ip = strings.TrimSpace(req.Header.Get("X-Real-Ip"))
	if ip != "" {
		return ip
	}
	if ip, _, err := net.SplitHostPort(strings.TrimSpace(req.RemoteAddr)); err == nil {
		return ip
	}
	return ""
}

func AccessLogFilter() gin.HandlerFunc {
	accessLogFormat := "[IP=%s] [HOST=%s] [USER_AGENT=%s] %s %s %dms %d %s"
	return func(c *gin.Context) {
		startTime := time.Now()

		defer func() {
			if err := recover(); err != nil {
				logrus.Errorf(fmt.Sprintf("http server handle req %s panic: %v", c.Request.RequestURI, err))
				util.PrintStack()

				latency := time.Since(startTime)
				logrus.Infof(accessLogFormat, getClientIp(c.Request), c.Request.Host,
					c.Request.Header.Get("User-Agent"),
					c.Request.Method, c.Request.RequestURI, latency.Milliseconds(), 502,
					c.Request.Header.Get("Content-Length"))
			}
		}()

		c.Next()

		status := c.Writer.Status()
		latency := time.Since(startTime)
		logrus.Infof(accessLogFormat, getClientIp(c.Request), c.Request.Host,
			c.Request.Header.Get("User-Agent"),
			c.Request.Method, c.Request.RequestURI, latency.Milliseconds(), status,
			c.Request.Header.Get("Content-Length"))
	}
}
