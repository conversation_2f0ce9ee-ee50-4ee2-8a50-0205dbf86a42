package restapi

import (
	"github.com/bilangpage/bilangpage-api/internal/dto"
	"github.com/bilangpage/bilangpage-api/internal/service"
	"github.com/bilangpage/bilangpage-api/internal/web"
	"github.com/bilangpage/bilangpage-api/internal/web/render"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func init() {
	translationAPI := &translationRestAPI{
		translationService: service.NewTranslationService(),
	}

	web.SetOptions(func(engine *gin.Engine) {
		// 翻译接口
		engine.POST("/translation", translationAPI.translate)
		// 用量信息接口
		engine.GET("/usageInfo", translationAPI.getUsageInfo)
	})
}

type translationRestAPI struct {
	translationService *service.TranslationService
}

// translate 翻译接口
func (t *translationRestAPI) translate(c *gin.Context) {
	// 验证请求头
	if err := validateHeaders(c); err != nil {
		render.ResponseBadRequest(c, err.Error())
		return
	}

	// 获取用户ID
	userID, err := getUserIDFromContext(c)
	if err != nil {
		render.ResponseUnauthorized(c, err.Error())
		return
	}

	// 解析请求参数
	var req dto.TranslationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		render.ResponseBadRequest(c, "Invalid request parameters: "+err.Error())
		return
	}

	// 验证请求参数
	if err := req.Validate(); err != nil {
		render.ResponseError(c, 400, 3, "Parameter error: "+err.Error())
		return
	}

	// 执行翻译
	response, err := t.translationService.Translate(userID, &req)
	if err != nil {
		logrus.WithFields(logrus.Fields{
			"user_id": userID,
			"service": req.Service,
			"error":   err.Error(),
		}).Error("Translation failed")

		// 根据错误类型返回不同的错误码
		if isQuotaError(err) {
			render.ResponseError(c, 400, 6, "Insufficient quota: "+err.Error())
		} else if isServiceUnavailableError(err) {
			render.ResponseError(c, 503, 4, "Translation service unavailable: "+err.Error())
		} else {
			render.ResponseError(c, 500, 1, "Translation failed: "+err.Error())
		}
		return
	}

	// 返回成功响应（使用code=0表示成功）
	successResponse := &dto.APIResponse{
		Code:    0,
		Message: "success",
		Data:    response,
	}
	c.JSON(200, successResponse)
}

// getUsageInfo 获取用量信息接口
func (t *translationRestAPI) getUsageInfo(c *gin.Context) {
	// 验证请求头
	if err := validateHeaders(c); err != nil {
		render.ResponseBadRequest(c, err.Error())
		return
	}

	// 获取用户ID
	userID, err := getUserIDFromContext(c)
	if err != nil {
		render.ResponseUnauthorized(c, err.Error())
		return
	}

	// 获取用量信息
	usageInfo, err := t.translationService.GetUserUsageInfo(userID)
	if err != nil {
		logrus.WithFields(logrus.Fields{
			"user_id": userID,
			"error":   err.Error(),
		}).Error("Failed to get usage info")
		
		render.ResponseError(c, 500, 1, "Failed to get usage info: "+err.Error())
		return
	}

	// 返回成功响应（使用code=0表示成功）
	successResponse := &dto.APIResponse{
		Code:    0,
		Message: "success",
		Data:    usageInfo,
	}
	c.JSON(200, successResponse)
}

// isQuotaError 判断是否为额度不足错误
func isQuotaError(err error) bool {
	errMsg := err.Error()
	return contains(errMsg, "insufficient quota") || 
		   contains(errMsg, "quota not found") ||
		   contains(errMsg, "额度不足")
}

// isServiceUnavailableError 判断是否为服务不可用错误
func isServiceUnavailableError(err error) bool {
	errMsg := err.Error()
	return contains(errMsg, "API key not configured") ||
		   contains(errMsg, "API error") ||
		   contains(errMsg, "service unavailable") ||
		   contains(errMsg, "failed to send request") ||
		   contains(errMsg, "timeout")
}

// contains 检查字符串是否包含子字符串（忽略大小写）
func contains(s, substr string) bool {
	return len(s) >= len(substr) && 
		   (s == substr || 
		    (len(s) > len(substr) && 
		     (s[:len(substr)] == substr || 
		      s[len(s)-len(substr):] == substr || 
		      indexOfSubstring(s, substr) >= 0)))
}

// indexOfSubstring 查找子字符串位置
func indexOfSubstring(s, substr string) int {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}
