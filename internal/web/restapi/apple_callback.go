package restapi

import (
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/bilangpage/bilangpage-api/internal/web"
	"github.com/gin-gonic/gin"
)

func init() {
	appleCallbackAPI := &appleCallbackRestAPI{}
	web.SetOptions(func(engine *gin.Engine) {
		engine.POST("/login/apple/callback", appleCallbackAPI.appleCallback)
	})
}

// AppleUserInfo Apple返回的用户信息结构
type AppleUserInfo struct {
	Name struct {
		FirstName string `json:"firstName"`
		LastName  string `json:"lastName"`
	} `json:"name"`
	Email string `json:"email"`
}

// AppleCallbackRequest Apple回调请求参数
type AppleCallbackRequest struct {
	Code  string `form:"code" binding:"required"`  // Apple返回的授权码
	State string `form:"state" binding:"required"` // CSRF状态参数
	User  string `form:"user"`                     // 用户信息JSON字符串
}

type appleCallbackRestAPI struct{}

func (a *appleCallbackRestAPI) appleCallback(c *gin.Context) {
	/**
	All form fields received:
	  user: [{"name":{"firstName":"春玲","lastName":"汪"},"email":"<EMAIL>"}]
	  state: [123456]
	  code: [c23f0562eb02a417fb1c2ffcebd5ad134.0.pryy.9pH1SU5BHGjwrITrha9khQ]
	*/

	var req AppleCallbackRequest
	if err := c.ShouldBind(&req); err != nil {
		c.String(http.StatusBadRequest, "Invalid callback parameters: %s", err.Error())
		return
	}

	// 解析用户信息
	var userInfo AppleUserInfo
	if req.User != "" {
		if err := json.Unmarshal([]byte(req.User), &userInfo); err != nil {
			fmt.Printf("Error parsing user info: %v\n", err)
		}
	}

	// 打印解析后的用户信息
	fmt.Printf("Parsed user info: firstName=%s, lastName=%s, email=%s\n",
		userInfo.Name.FirstName, userInfo.Name.LastName, userInfo.Email)

	// 打印所有表单字段，包括未在结构体中定义的字段
	fmt.Println("All form fields received:")
	for key, values := range c.Request.Form {
		fmt.Printf("  %s: %v\n", key, values)
	}

	// 构建重定向URL，使用解析后的用户信息
	redirectURL := fmt.Sprintf("https://bilangepage.com/login/apple?code=%s&email=%s&firstName=%s&lastName=%s",
		req.Code, userInfo.Email, userInfo.Name.FirstName, userInfo.Name.LastName)

	c.Redirect(http.StatusFound, redirectURL)
}
