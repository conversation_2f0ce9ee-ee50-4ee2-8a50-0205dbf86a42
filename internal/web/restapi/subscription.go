package restapi

import (
	"fmt"
	"github.com/bilangpage/bilangpage-api/internal/dto"
	"github.com/bilangpage/bilangpage-api/internal/service"
	"github.com/bilangpage/bilangpage-api/internal/web"
	"github.com/bilangpage/bilangpage-api/internal/web/render"
	"github.com/gin-gonic/gin"
)

func init() {
	subscriptionAPI := &subscriptionRestAPI{
		subscriptionService: service.NewSubscriptionService(),
	}

	web.SetOptions(func(engine *gin.Engine) {
		subscriptionGroup := engine.Group("/api/subscription")
		{
			subscriptionGroup.POST("/sync", subscriptionAPI.syncSubscription)
			subscriptionGroup.GET("/status", subscriptionAPI.getSubscriptionStatus)
		}
	})
}

type subscriptionRestAPI struct {
	subscriptionService *service.SubscriptionService
}

// syncSubscription 订阅同步接口
func (s *subscriptionRestAPI) syncSubscription(c *gin.Context) {
	// 验证请求头
	if err := validateHeaders(c); err != nil {
		render.ResponseBadRequest(c, err.Error())
		return
	}

	// 获取用户ID
	userID, err := getUserIDFromContext(c)
	if err != nil {
		render.ResponseUnauthorized(c, err.Error())
		return
	}

	// 解析请求参数
	var req dto.SubscriptionSyncRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		render.ResponseBadRequest(c, "Invalid request parameters: "+err.Error())
		return
	}

	// 同步订阅
	response, err := s.subscriptionService.SyncSubscription(userID, &req)
	if err != nil {
		render.ResponseInternalServerError(c, err.Error())
		return
	}

	// 构建成功响应
	successResponse := &dto.APIResponse{
		Code:    200,
		Message: "Subscription sync successful",
		Data:    response,
	}
	c.JSON(200, successResponse)
}

// getSubscriptionStatus 订阅状态查询接口
func (s *subscriptionRestAPI) getSubscriptionStatus(c *gin.Context) {
	// 验证请求头
	if err := validateHeaders(c); err != nil {
		render.ResponseBadRequest(c, err.Error())
		return
	}

	// 获取用户ID
	userID, err := getUserIDFromContext(c)
	if err != nil {
		render.ResponseUnauthorized(c, err.Error())
		return
	}

	// 获取订阅状态
	response, err := s.subscriptionService.GetSubscriptionStatus(userID)
	if err != nil {
		render.ResponseInternalServerError(c, "Failed to get subscription status")
		return
	}

	// 构建成功响应
	var message string
	if response.IsValid {
		message = "Subscription status retrieved successfully"
	} else {
		message = "No active subscription found"
	}

	successResponse := &dto.APIResponse{
		Code:    200,
		Message: message,
		Data:    response,
	}
	c.JSON(200, successResponse)
}

// validateHeaders 验证请求头
func validateHeaders(c *gin.Context) error {
	// 检查必需的请求头
	deviceID := c.GetHeader("X-Device-ID")
	timestamp := c.GetHeader("X-Timestamp")
	if deviceID == "" {
		return fmt.Errorf("X-Device-ID header is required")
	}
	if timestamp == "" {
		return fmt.Errorf("X-Timestamp header is required")
	}
	return nil
}

// getUserIDFromContext 从context中获取用户ID
func getUserIDFromContext(c *gin.Context) (string, error) {
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		return "", fmt.Errorf("invalid or expired token")
	}
	userID, ok := userIDInterface.(string)
	if !ok {
		return "", fmt.Errorf("invalid token")
	}
	return userID, nil
}
