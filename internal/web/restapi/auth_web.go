package restapi

import (
	"github.com/sirupsen/logrus"
	"strings"

	"github.com/bilangpage/bilangpage-api/internal/dto"
	"github.com/bilangpage/bilangpage-api/internal/service"
	"github.com/bilangpage/bilangpage-api/internal/web"
	"github.com/bilangpage/bilangpage-api/internal/web/render"
	"github.com/gin-gonic/gin"
)

func init() {
	authWebAPI := &authWebRestAPI{
		userService:     service.NewUserService(),
		jwtService:      service.NewJWTService(),
		appleWebService: service.NewAppleWebOAuthService(),
		oauthService:    service.NewOAuthService(),
	}

	web.SetOptions(func(engine *gin.Engine) {
		authGroup := engine.Group("/api/auth/web")
		{
			authGroup.POST("/apple", authWebAPI.appleLogin)
		}
	})
}

type authWebRestAPI struct {
	userService     *service.UserService
	jwtService      *service.JWTService
	appleWebService *service.AppleWebOAuthService
	oauthService    *service.OAuthService
}

// appleLogin Apple Web登录接口
func (a *authWebRestAPI) appleLogin(c *gin.Context) {
	var req struct {
		Code      string `json:"code" binding:"required"` // Apple返回的授权码
		Email     string `json:"email,omitempty"`         // 邮箱（可选）
		FirstName string `json:"firstName,omitempty"`     // 用户名（可选）
		LastName  string `json:"lastName,omitempty"`      // 用户姓（可选）
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		logrus.Errorf("Invalid request parameters: %v", err)
		render.ResponseBadRequest(c, "Invalid request parameters: "+err.Error())
		return
	}
	// 验证授权码并获取用户信息
	appleUser, err := a.appleWebService.VerifyWebCode(req.Code)
	if err != nil {
		logrus.Errorf("Failed to verify Apple authorization code: %v", err)
		render.ResponseBadRequest(c, "Failed to verify Apple authorization code: "+err.Error())
		return
	}
	// 使用请求传递的
	if appleUser.Email == "" {
		appleUser.Email = req.Email
	}
	// 如果前端提供了用户名信息，使用前端提供的
	if req.FirstName != "" || req.LastName != "" {
		appleUser.Name.FirstName = req.FirstName
		appleUser.Name.LastName = req.LastName
	}
	// 处理用户信息
	nickname := ""
	if appleUser.Name.FirstName != "" || appleUser.Name.LastName != "" {
		nickname = strings.TrimSpace(appleUser.Name.FirstName + " " + appleUser.Name.LastName)
	}
	// 查找或创建用户
	user, err := a.userService.FindOrCreateUser(
		"apple",
		appleUser.Sub,
		appleUser.Sub,
		nickname,
		"", // Apple默认不提供头像
		appleUser.Email,
	)
	if err != nil {
		logrus.Errorf("Failed to find or create user: %v", err)
		render.ResponseInternalServerError(c, "Failed to process user")
		return
	}
	// 生成JWT token
	token, err := a.jwtService.GenerateToken(user.ID, user.Username, user.Provider, false)
	if err != nil {
		logrus.Errorf("Failed to generate token: %v", err)
		render.ResponseInternalServerError(c, "Failed to generate token")
		return
	}
	// 构建响应
	response := &dto.LoginResponse{
		Token:     token,
		ExpiresIn: a.jwtService.GetExpiresIn(false),
		User:      dto.ConvertUserToUserInfo(user, false),
	}
	render.ResponseSuccess(c, response)
}
