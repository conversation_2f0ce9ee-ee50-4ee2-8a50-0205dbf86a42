package restapi

import (
	"github.com/bilangpage/bilangpage-api/internal/web"
	"github.com/bilangpage/bilangpage-api/internal/web/render"
	"github.com/gin-gonic/gin"
)

func init() {
	heartbeat := &heartbeatRestAPI{}
	web.SetOptions(func(engine *gin.Engine) {
		engine.GET("/status", heartbeat.heartbeat)
	})
}

type heartbeatRestAPI struct {
}

func (heartbeat *heartbeatRestAPI) heartbeat(c *gin.Context) {
	render.Response(c, nil)
}
