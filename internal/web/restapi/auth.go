package restapi

import (
	"github.com/bilangpage/bilangpage-api/internal/dto"
	"github.com/bilangpage/bilangpage-api/internal/service"
	"github.com/bilangpage/bilangpage-api/internal/web"
	"github.com/bilangpage/bilangpage-api/internal/web/render"
	"github.com/gin-gonic/gin"
	"strings"
)

func init() {
	authAPI := &authRestAPI{
		userService:  service.NewUserService(),
		jwtService:   service.NewJWTService(),
		oauthService: service.NewOAuthService(),
	}

	web.SetOptions(func(engine *gin.Engine) {
		authGroup := engine.Group("/api/auth")
		{
			authGroup.POST("/apple", authAPI.appleLogin)
			authGroup.GET("/user", authAPI.getUserInfo)
		}
	})
}

type authRestAPI struct {
	userService  *service.UserService
	jwtService   *service.JWTService
	oauthService *service.OAuthService
}

// appleLogin Apple登录接口
func (a *authRestAPI) appleLogin(c *gin.Context) {
	var req dto.AppleLoginRequest
	if err := c.ShouldBind(&req); err != nil {
		render.ResponseBadRequest(c, "Invalid request parameters: "+err.Error())
		return
	}

	// 验证请求参数
	if err := req.Validate(); err != nil {
		render.ResponseBadRequest(c, err.Error())
		return
	}

	// 验证Apple凭据
	appleUser, err := a.oauthService.VerifyAppleCredentials(req)
	if err != nil {
		// 提供更具体的错误信息
		if strings.Contains(err.Error(), "invalid issuer") {
			render.ResponseBadRequest(c, "Invalid Apple token issuer")
		} else if strings.Contains(err.Error(), "invalid audience") {
			render.ResponseBadRequest(c, "Invalid Apple token audience")
		} else if strings.Contains(err.Error(), "token expired") {
			render.ResponseBadRequest(c, "Apple token has expired")
		} else if strings.Contains(err.Error(), "failed to parse JWT") {
			render.ResponseBadRequest(c, "Invalid Apple token format")
		} else if strings.Contains(err.Error(), "public key") {
			render.ResponseInternalServerError(c, "Apple authentication service error")
		} else {
			render.ResponseBadRequest(c, "Apple authentication failed: "+err.Error())
		}
		return
	}

	// 处理用户信息
	nickname := ""
	if appleUser.Name.FirstName != "" || appleUser.Name.LastName != "" {
		nickname = strings.TrimSpace(appleUser.Name.FirstName + " " + appleUser.Name.LastName)
	}
	// 如果没有姓名信息，保持nickname为空，让UserService保持已有的nickname不变

	// 查找或创建用户
	user, err := a.userService.FindOrCreateUser(
		"apple",
		appleUser.Sub,
		appleUser.Sub,
		nickname, // 可能为空，这样不会覆盖已有的nickname
		"",       // Apple默认不提供头像
		appleUser.Email,
	)
	if err != nil {
		render.ResponseInternalServerError(c, "Failed to process user")
		return
	}

	// 生成JWT token
	token, err := a.jwtService.GenerateToken(user.ID, user.Username, user.Provider, true)
	if err != nil {
		render.ResponseInternalServerError(c, "Failed to generate token")
		return
	}

	// 构建响应
	response := &dto.LoginResponse{
		Token:     token,
		ExpiresIn: a.jwtService.GetExpiresIn(true),
		User:      dto.ConvertUserToUserInfo(user, false),
	}

	render.ResponseSuccess(c, response)
}

// getUserInfo 获取用户信息接口
func (a *authRestAPI) getUserInfo(c *gin.Context) {
	// 从context中获取用户ID（由认证中间件设置）
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		render.ResponseUnauthorized(c, "Invalid or expired token")
		return
	}

	userID, ok := userIDInterface.(string)
	if !ok {
		render.ResponseUnauthorized(c, "Invalid token")
		return
	}

	// 获取用户信息
	user, err := a.userService.GetUserByID(userID)
	if err != nil {
		render.ResponseUnauthorized(c, "Invalid or expired token")
		return
	}

	// 返回用户信息（包含时间戳）
	render.ResponseSuccess(c, dto.ConvertUserToUserInfo(user, true))
}
