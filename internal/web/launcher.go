package web

import (
	"github.com/bilangpage/bilangpage-api/internal/config"
	"github.com/bilangpage/bilangpage-api/internal/web/filter"
	"github.com/bilangpage/bilangpage-api/pkg/util"
	"github.com/gin-gonic/gin"
	"strconv"
)

var opts []func(*gin.Engine)

func SetOptions(opt func(engine *gin.Engine)) {
	opts = append(opts, opt)
}

func init() {
	gin.SetMode(gin.ReleaseMode)
}

func Start() {
	engine := gin.New()

	middleware := []gin.HandlerFunc{
		filter.AccessLogFilter(),
		filter.TokenAuthFilter(),
	}
	engine.Use(middleware...)

	for _, opt := range opts {
		opt(engine)
	}

	if err := engine.Run(":" + strconv.Itoa(config.C.Server.Port)); err != nil {
		util.AbnormalExit(err)
	}
}
