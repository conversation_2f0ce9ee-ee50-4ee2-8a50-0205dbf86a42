package model

import (
	"time"
)

// User 用户模型
type User struct {
	ID         string    `json:"id" gorm:"primaryKey;column:id"`
	Username   string    `json:"username" gorm:"column:username;not null"`
	Nickname   string    `json:"nickname" gorm:"column:nickname"`
	Avatar     string    `json:"avatar" gorm:"column:avatar"`
	Email      string    `json:"email" gorm:"column:email"`
	Provider   string    `json:"provider" gorm:"column:provider;not null"`
	ProviderID string    `json:"-" gorm:"column:provider_id;not null"`
	CreatedAt  time.Time `json:"created_at" gorm:"column:created_at"`
	LastLogin  time.Time `json:"last_login" gorm:"column:last_login"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}
