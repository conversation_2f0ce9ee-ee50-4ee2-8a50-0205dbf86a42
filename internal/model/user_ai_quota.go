package model

import "time"

// UserAIQuota 用户AI翻译额度
type UserAIQuota struct {
	UserId                   string     `json:"userId" gorm:"primaryKey"`
	TotalQuota               int        `json:"totalQuota" gorm:"total_quota"`                            // 当前周期总额度
	UsedQuota                int        `json:"usedQuota" gorm:"used_quota"`                              // 已使用额度
	PendingQuota             int        `json:"pendingQuota" gorm:"pending_quota"`                        // 待生效额度（续期后等待重置时生效）
	CycleStartAt             time.Time  `json:"cycleStartAt" gorm:"cycle_start_at"`                       // 当前周期开始时间
	NextResetAt              time.Time  `json:"nextResetAt" gorm:"next_reset_at"`                         // 下次重置时间
	PendingNextResetAt       *time.Time `json:"pendingNextResetAt" gorm:"pending_next_reset_at"`          // 待生效的下次重置时间（续期时设置）
	OriginalResetDay         int        `json:"originalResetDay" gorm:"original_reset_day"`               // 原始重置日期（1-31）
	LastUsedAt               *time.Time `json:"lastUsedAt" gorm:"last_used_at"`                           // 最后使用时间
	CreatedAt                time.Time  `json:"createdAt" gorm:"created_at"`
	UpdatedAt                time.Time  `json:"updatedAt" gorm:"updated_at"`
}

func (UserAIQuota) TableName() string {
	return "user_ai_quota"
}

// ShouldReset 检查是否应该重置用量
func (q *UserAIQuota) ShouldReset() bool {
	return time.Now().After(q.NextResetAt)
}

// CalculateNextResetTime 计算下次重置时间（兜底逻辑，按Apple订阅规则）
// 优先使用订阅过期时间，如果没有则使用Apple订阅规则作为兜底
func (q *UserAIQuota) CalculateNextResetTime(baseTime time.Time) time.Time {
	// 使用原始重置日期，如果没有设置则使用baseTime的日期
	originalDay := q.OriginalResetDay
	if originalDay == 0 {
		originalDay = baseTime.Day()
		q.OriginalResetDay = originalDay // 记录原始日期
	}

	// 获取下个月
	nextYear := baseTime.Year()
	nextMonth := baseTime.Month() + 1
	if nextMonth > 12 {
		nextMonth = 1
		nextYear++
	}

	// 获取下个月的最后一天
	lastDayOfNextMonth := time.Date(nextYear, nextMonth+1, 0, 0, 0, 0, 0, baseTime.Location()).Day()

	// 确定重置日期（按Apple规则）
	var resetDay int
	if originalDay <= lastDayOfNextMonth {
		// 下个月有这一天，使用原始日期
		resetDay = originalDay
	} else {
		// 下个月没有这一天，使用下个月的最后一天
		resetDay = lastDayOfNextMonth
	}

	// 构建重置时间（保持原始的时分秒）
	resetTime := time.Date(nextYear, nextMonth, resetDay,
		baseTime.Hour(), baseTime.Minute(), baseTime.Second(),
		baseTime.Nanosecond(), baseTime.Location())

	return resetTime
}

// ResetUsage 重置用量并更新周期时间（应用待生效额度和重置时间）
func (q *UserAIQuota) ResetUsage() {
	now := time.Now()
	q.UsedQuota = 0 // 重置已使用额度

	// 如果有待生效额度，应用到当前周期
	if q.PendingQuota > 0 {
		q.TotalQuota = q.PendingQuota
		q.PendingQuota = 0 // 清空待生效额度
	}

	q.CycleStartAt = now

	// 应用待生效的重置时间（如果有的话）
	if q.PendingNextResetAt != nil && !q.PendingNextResetAt.IsZero() {
		q.NextResetAt = *q.PendingNextResetAt
		q.OriginalResetDay = q.PendingNextResetAt.Day()
		q.PendingNextResetAt = nil // 清空待生效的重置时间
	} else if q.TotalQuota > 0 {
		// 只有在有总额度时才计算下次重置时间
		// 如果用户没有续订（总额度为0），不设置下次重置时间
		// 如果NextResetAt为零值或已过期，使用Apple规则计算下次重置时间作为兜底
		if q.NextResetAt.IsZero() || q.NextResetAt.Before(now) {
			q.NextResetAt = q.CalculateNextResetTime(now)
		}
	}

	q.UpdatedAt = now
}

// UserAIQuotaLog 用户AI翻译额度变动日志
type UserAIQuotaLog struct {
	Id        int64     `json:"id" gorm:"primaryKey"`
	UserId    string    `json:"userId" gorm:"index:idx_user_id"`
	QuotaCnt  int       `json:"quotaCnt" gorm:"quota_cnt"`   // 变动额度，正数添加，负数减少
	Reason    string    `json:"reason" gorm:"reason"`        // 变动原因
	CreatedAt time.Time `json:"createdAt" gorm:"created_at"` // 变动时间
}

func (UserAIQuotaLog) TableName() string {
	return "user_ai_quota_log"
}
