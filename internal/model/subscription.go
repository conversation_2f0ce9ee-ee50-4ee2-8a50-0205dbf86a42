package model

import (
	"time"
)

// Subscription 订阅模型
type Subscription struct {
	ID                    int64  `json:"id" gorm:"primaryKey;column:id"`
	UserID                string `json:"user_id" gorm:"column:user_id;not null;index:idx_user_expires"`
	ProductID             string `json:"product_id" gorm:"column:product_id;not null"`
	TransactionID         string `json:"transaction_id" gorm:"column:transaction_id;uniqueIndex;not null"`
	OriginalTransactionID string `json:"original_transaction_id" gorm:"column:original_transaction_id;not null"`
	ExpiresAt             int64  `json:"expires_at" gorm:"column:expires_at;not null;index:idx_user_expires"` // Unix timestamp in milliseconds
	IsValid               bool   `json:"is_valid" gorm:"column:is_valid;default:true"`
	Platform              string `json:"platform" gorm:"column:platform;default:'macOS'"`
	CreatedAt             int64  `json:"created_at" gorm:"column:created_at;not null"` // Unix timestamp in milliseconds
	UpdatedAt             int64  `json:"updated_at" gorm:"column:updated_at;not null"` // Unix timestamp in milliseconds
}

// TableName 指定表名
func (Subscription) TableName() string {
	return "subscriptions"
}

// IsActive 检查订阅是否有效且未过期
func (s *Subscription) IsActive() bool {
	return s.IsValid && s.ExpiresAt > time.Now().UnixMilli()
}
