package model

import "time"

// TranslationUsageLog 翻译用量日志
type TranslationUsageLog struct {
	ID         int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID     string    `json:"user_id" gorm:"column:user_id;not null;index"`
	Service    string    `json:"service" gorm:"column:service;not null"`      // 翻译服务类型
	CharCount  int       `json:"char_count" gorm:"column:char_count"`         // 字符数量
	UsageCount int       `json:"usage_count" gorm:"column:usage_count"`       // 实际计费用量（等于字符数）
	TargetLang string    `json:"target_lang" gorm:"column:target_lang"`       // 目标语言
	TextCount  int       `json:"text_count" gorm:"column:text_count"`         // 翻译文本条数
	CreatedAt  time.Time `json:"created_at" gorm:"column:created_at"`
}

func (TranslationUsageLog) TableName() string {
	return "translation_usage_log"
}
