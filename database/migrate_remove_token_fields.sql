-- 迁移脚本：移除token相关字段，统一使用字符数计算用量
-- 执行前请备份数据库

-- 1. 首先更新usage_count为char_count的值（如果char_count不为0）
UPDATE translation_usage_log 
SET usage_count = char_count 
WHERE char_count > 0 AND usage_count != char_count;

-- 2. 对于没有char_count但有input_tokens的记录，使用input_tokens作为usage_count
UPDATE translation_usage_log 
SET usage_count = input_tokens,
    char_count = input_tokens
WHERE char_count = 0 AND input_tokens > 0;

-- 3. 删除token相关字段
ALTER TABLE translation_usage_log DROP COLUMN IF EXISTS input_tokens;
ALTER TABLE translation_usage_log DROP COLUMN IF EXISTS output_tokens;

-- 4. 确保char_count不为空
ALTER TABLE translation_usage_log ALTER COLUMN char_count SET NOT NULL;
ALTER TABLE translation_usage_log ALTER COLUMN char_count SET DEFAULT 0;

-- 5. 添加约束确保char_count和usage_count一致
ALTER TABLE translation_usage_log ADD CONSTRAINT check_char_usage_consistency 
CHECK (char_count = usage_count);
