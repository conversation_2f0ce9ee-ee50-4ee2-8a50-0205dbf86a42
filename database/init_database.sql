-- BiLangPage API PostgreSQL 数据库初始化脚本
-- 创建时间: 2025-09-07
-- 版本: v1.0
-- 数据库类型: PostgreSQL

-- =====================================================
-- 1. 用户表
-- =====================================================
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(255) PRIMARY KEY,
    username VARCHAR(255) NOT NULL,
    nickname VARCHAR(255),
    avatar TEXT,
    email VARCHAR(255),
    provider VARCHAR(50) NOT NULL CHECK (provider IN ('apple','email')),
    provider_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_provider_user UNIQUE (provider, provider_id)
);

-- 用户表索引
CREATE INDEX IF NOT EXISTS idx_users_provider ON users(provider);
CREATE INDEX IF NOT EXISTS idx_users_provider_id ON users(provider_id);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email) WHERE email IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
CREATE INDEX IF NOT EXISTS idx_users_last_login ON users(last_login);

-- 用户表注释
COMMENT ON TABLE users IS '用户表，存储Email登录和Apple登录的用户信息';
COMMENT ON COLUMN users.id IS '用户唯一标识，32位十六进制字符串';
COMMENT ON COLUMN users.username IS '用户名，对应Apple用户标识符';
COMMENT ON COLUMN users.nickname IS '显示昵称，用于界面展示';
COMMENT ON COLUMN users.avatar IS '用户头像URL';
COMMENT ON COLUMN users.email IS '用户邮箱地址，可能为空';
COMMENT ON COLUMN users.provider IS 'OAuth提供商，apple或email';
COMMENT ON COLUMN users.provider_id IS 'OAuth提供商的用户ID';
COMMENT ON COLUMN users.created_at IS '用户创建时间';
COMMENT ON COLUMN users.last_login IS '最后登录时间';

-- =====================================================
-- 2. 订阅表
-- =====================================================
CREATE TABLE IF NOT EXISTS subscriptions (
    id BIGSERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    product_id VARCHAR(100) NOT NULL,
    transaction_id VARCHAR(50) UNIQUE NOT NULL,
    original_transaction_id VARCHAR(50) NOT NULL,
    expires_at BIGINT NOT NULL,
    is_valid BOOLEAN DEFAULT TRUE,
    platform VARCHAR(20) DEFAULT 'iOS',
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL
);

-- 订阅表索引
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_expires ON subscriptions (user_id, expires_at);
CREATE UNIQUE INDEX IF NOT EXISTS idx_subscriptions_transaction ON subscriptions (transaction_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_original_transaction ON subscriptions (original_transaction_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions (user_id);

-- 订阅表注释
COMMENT ON TABLE subscriptions IS '用户订阅表';
COMMENT ON COLUMN subscriptions.id IS '主键ID';
COMMENT ON COLUMN subscriptions.user_id IS '用户ID，关联users表';
COMMENT ON COLUMN subscriptions.product_id IS '产品ID，标识订阅类型';
COMMENT ON COLUMN subscriptions.transaction_id IS '交易ID，用于防重复';
COMMENT ON COLUMN subscriptions.original_transaction_id IS '原始交易ID';
COMMENT ON COLUMN subscriptions.expires_at IS '订阅过期时间(Unix时间戳-毫秒)';
COMMENT ON COLUMN subscriptions.is_valid IS '订阅是否有效';
COMMENT ON COLUMN subscriptions.platform IS '平台标识，iOS, macOS, Chrome';
COMMENT ON COLUMN subscriptions.created_at IS '创建时间(Unix时间戳-毫秒)';
COMMENT ON COLUMN subscriptions.updated_at IS '更新时间(Unix时间戳-毫秒)';

-- =====================================================
-- 3. 用户AI翻译额度表
-- =====================================================
CREATE TABLE IF NOT EXISTS user_ai_quota (
    user_id VARCHAR(255) NOT NULL PRIMARY KEY,
    total_quota INTEGER NOT NULL DEFAULT 0,
    used_quota INTEGER NOT NULL DEFAULT 0,
    pending_quota INTEGER NOT NULL DEFAULT 0,
    cycle_start_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    next_reset_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    original_reset_day INTEGER DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 用户AI额度表索引
CREATE INDEX IF NOT EXISTS idx_user_ai_quota_next_reset ON user_ai_quota(next_reset_at);
CREATE INDEX IF NOT EXISTS idx_user_ai_quota_user_id ON user_ai_quota(user_id);

-- 用户AI额度表注释
COMMENT ON TABLE user_ai_quota IS '用户AI翻译额度表';
COMMENT ON COLUMN user_ai_quota.user_id IS '用户ID，关联users表';
COMMENT ON COLUMN user_ai_quota.total_quota IS '当前周期总额度';
COMMENT ON COLUMN user_ai_quota.used_quota IS '已使用额度';
COMMENT ON COLUMN user_ai_quota.pending_quota IS '待生效额度（续期后等待重置时生效）';
COMMENT ON COLUMN user_ai_quota.cycle_start_at IS '当前周期开始时间';
COMMENT ON COLUMN user_ai_quota.next_reset_at IS '下次重置时间（按Apple订阅规则）';
COMMENT ON COLUMN user_ai_quota.original_reset_day IS '原始重置日期（1-31），用于处理月末日期问题';
COMMENT ON COLUMN user_ai_quota.last_used_at IS '最后使用时间';
COMMENT ON COLUMN user_ai_quota.created_at IS '创建时间';
COMMENT ON COLUMN user_ai_quota.updated_at IS '更新时间';

-- =====================================================
-- 4. 用户AI翻译额度变动日志表
-- =====================================================
CREATE TABLE IF NOT EXISTS user_ai_quota_log (
    id BIGSERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    quota_cnt INTEGER NOT NULL,
    reason VARCHAR(500) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 用户AI额度日志表索引
CREATE INDEX IF NOT EXISTS idx_user_ai_quota_log_user_id ON user_ai_quota_log(user_id);
CREATE INDEX IF NOT EXISTS idx_user_ai_quota_log_created_at ON user_ai_quota_log(created_at);
CREATE INDEX IF NOT EXISTS idx_user_ai_quota_log_user_created ON user_ai_quota_log(user_id, created_at);

-- 用户AI额度日志表注释
COMMENT ON TABLE user_ai_quota_log IS '用户AI翻译额度变动日志表';
COMMENT ON COLUMN user_ai_quota_log.id IS '日志ID';
COMMENT ON COLUMN user_ai_quota_log.user_id IS '用户ID，关联users表';
COMMENT ON COLUMN user_ai_quota_log.quota_cnt IS '变动额度，正数添加，负数减少';
COMMENT ON COLUMN user_ai_quota_log.reason IS '变动原因';
COMMENT ON COLUMN user_ai_quota_log.created_at IS '变动时间';

-- =====================================================
-- 5. 翻译用量日志表
-- =====================================================
CREATE TABLE IF NOT EXISTS translation_usage_log (
    id BIGSERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    service VARCHAR(50) NOT NULL,
    char_count INTEGER NOT NULL DEFAULT 0,
    usage_count INTEGER NOT NULL DEFAULT 0,
    target_lang VARCHAR(10) NOT NULL,
    text_count INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT check_char_usage_consistency CHECK (char_count = usage_count)
);

-- 翻译用量日志表索引
CREATE INDEX IF NOT EXISTS idx_translation_usage_user_id ON translation_usage_log(user_id);
CREATE INDEX IF NOT EXISTS idx_translation_usage_created_at ON translation_usage_log(created_at);
CREATE INDEX IF NOT EXISTS idx_translation_usage_service ON translation_usage_log(service);
CREATE INDEX IF NOT EXISTS idx_translation_usage_user_service ON translation_usage_log(user_id, service);
CREATE INDEX IF NOT EXISTS idx_translation_usage_user_created ON translation_usage_log(user_id, created_at);

-- 翻译用量日志表注释
COMMENT ON TABLE translation_usage_log IS '翻译用量日志表（统一使用字符数计算用量）';
COMMENT ON COLUMN translation_usage_log.id IS '日志ID';
COMMENT ON COLUMN translation_usage_log.user_id IS '用户ID，关联users表';
COMMENT ON COLUMN translation_usage_log.service IS '翻译服务类型（deepl, deepseek-v3, gpt-4o-mini）';
COMMENT ON COLUMN translation_usage_log.char_count IS '字符数量';
COMMENT ON COLUMN translation_usage_log.usage_count IS '用量计数（与字符数相等）';
COMMENT ON COLUMN translation_usage_log.target_lang IS '目标语言代码';
COMMENT ON COLUMN translation_usage_log.text_count IS '翻译文本数量';
COMMENT ON COLUMN translation_usage_log.created_at IS '创建时间';

-- =====================================================
-- 6. 外键约束（可选，根据需要启用）
-- =====================================================
-- 注意：由于使用了字符串类型的user_id，这些外键约束是可选的
-- 如果需要严格的引用完整性，可以取消注释以下语句

-- ALTER TABLE subscriptions
-- ADD CONSTRAINT fk_subscriptions_user_id
-- FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- ALTER TABLE user_ai_quota
-- ADD CONSTRAINT fk_user_ai_quota_user_id
-- FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- ALTER TABLE user_ai_quota_log
-- ADD CONSTRAINT fk_user_ai_quota_log_user_id
-- FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- ALTER TABLE translation_usage_log
-- ADD CONSTRAINT fk_translation_usage_log_user_id
-- FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- =====================================================
-- 7. 数据库初始化完成
-- =====================================================
-- 初始化完成提示
SELECT 'BiLangPage API PostgreSQL 数据库初始化完成' AS status,
       'v1.0' AS version,
       NOW() AS initialized_at;
