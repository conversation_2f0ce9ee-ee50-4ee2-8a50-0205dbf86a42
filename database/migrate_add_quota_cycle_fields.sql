-- 添加用量周期管理字段
ALTER TABLE user_ai_quota 
ADD COLUMN cycle_start_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN next_reset_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN original_reset_day INTEGER DEFAULT 0,
ADD COLUMN pending_quota INTEGER DEFAULT 0;

-- 为现有记录设置初始值
UPDATE user_ai_quota 
SET 
    cycle_start_at = created_at,
    next_reset_at = created_at + INTERVAL '1 month',
    original_reset_day = EXTRACT(DAY FROM created_at),
    pending_quota = 0
WHERE cycle_start_at IS NULL OR next_reset_at IS NULL OR original_reset_day = 0;

-- 添加索引以提高查询性能
CREATE INDEX idx_user_ai_quota_next_reset ON user_ai_quota(next_reset_at);

-- 添加注释
COMMENT ON COLUMN user_ai_quota.cycle_start_at IS '当前周期开始时间';
COMMENT ON COLUMN user_ai_quota.next_reset_at IS '下次重置时间（按Apple订阅规则）';
COMMENT ON COLUMN user_ai_quota.original_reset_day IS '原始重置日期（1-31），用于处理月末日期问题';
COMMENT ON COLUMN user_ai_quota.pending_quota IS '待生效额度（续期后等待重置时生效）';
