# BiLangPage API 数据库初始化

## 概述

本目录包含BiLangPage API的PostgreSQL数据库初始化脚本。

## 文件说明

- `init_database.sql` - 完整的数据库初始化脚本，包含所有表结构、索引和注释

## 数据库表结构

### 1. users - 用户表
存储Email登录和Apple登录的用户信息
- 主键：`id` (VARCHAR(255))
- 唯一约束：`(provider, provider_id)`
- 支持的provider：`apple`, `email`

### 2. subscriptions - 订阅表
存储用户的订阅信息
- 主键：`id` (BIGSERIAL)
- 唯一约束：`transaction_id`
- 时间戳使用Unix毫秒格式

### 3. user_ai_quota - 用户AI翻译额度表
管理用户的翻译额度和重置周期
- 主键：`user_id` (VARCHAR(255))
- 支持Apple订阅规则的月度重置
- 包含待生效额度字段

### 4. user_ai_quota_log - 用户AI翻译额度变动日志表
记录用户额度的所有变动
- 主键：`id` (BIGSERIAL)
- 记录变动原因和时间

### 5. translation_usage_log - 翻译用量日志表
记录所有翻译请求的用量统计
- 主键：`id` (BIGSERIAL)
- 统一使用字符数计算用量
- 约束：`char_count = usage_count`

## 使用方法

### 1. 连接到PostgreSQL数据库
```bash
psql -h localhost -U your_username -d your_database
```

### 2. 执行初始化脚本
```bash
psql -h localhost -U your_username -d your_database -f database/init_database.sql
```

或者在psql命令行中：
```sql
\i database/init_database.sql
```

### 3. 验证初始化结果
```sql
-- 查看所有表
\dt

-- 查看表结构
\d users
\d subscriptions
\d user_ai_quota
\d user_ai_quota_log
\d translation_usage_log

-- 查看索引
\di
```

## 重要特性

### 1. PostgreSQL特性
- 使用`BIGSERIAL`作为自增主键
- 使用`TIMESTAMP WITH TIME ZONE`处理时区
- 使用`CHECK`约束确保数据完整性
- 完整的`COMMENT`注释系统

### 2. 索引优化
- 为常用查询字段创建索引
- 复合索引优化多字段查询
- 条件索引（如email的非空索引）

### 3. 数据完整性
- 唯一约束防止重复数据
- 检查约束确保数据有效性
- 可选的外键约束（默认注释掉）

### 4. Apple订阅支持
- 支持月末日期的智能处理
- 待生效额度机制
- 原始重置日期记录

## 外键约束

默认情况下，外键约束被注释掉了。如果需要严格的引用完整性，可以取消注释相关的`ALTER TABLE`语句。

## 注意事项

1. **时间戳格式**：订阅表使用Unix毫秒时间戳，其他表使用PostgreSQL的TIMESTAMP WITH TIME ZONE
2. **字符编码**：确保数据库使用UTF-8编码
3. **权限**：执行脚本的用户需要有创建表和索引的权限
4. **备份**：在生产环境执行前请先备份数据库

## 版本信息

- 版本：v1.0
- 创建时间：2024-12-07
- 数据库类型：PostgreSQL
- 兼容版本：PostgreSQL 12+
