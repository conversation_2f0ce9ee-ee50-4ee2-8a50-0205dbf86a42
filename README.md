# BiLangPage 服务端

BiLangPage Pro 网页双语翻译插件的后端API服务。

## 功能特性

- **多翻译服务支持**: 支持 DeepL、DeepSeek-v3、OpenRouter (GPT-4o-mini) 等翻译服务
- **智能翻译策略**: DeepL 使用并发调用，AI服务使用批量翻译（失败时自动回退到单个翻译）
- **用量统计**: 统一使用Unicode字符数计算用量，简化计费逻辑
- **高性能缓存**: 内存缓存用量和订阅信息，5分钟缓存时间，支持高并发访问
- **异步用量扣减**: 翻译成功后先扣减内存值，异步批量更新数据库，减少数据库压力
- **用户认证**: JWT token 认证，支持 Apple 登录
- **额度管理**: 用户AI翻译额度管理，翻译成功后才扣减额度

## 支持的翻译服务

- **deepl**: DeepL 翻译服务（并发调用）
- **deepseek-v3**: DeepSeek v3 模型（合并请求）
- **gpt-4o-mini**: OpenRouter GPT-4o-mini（合并请求）

## 支持的语言

- `zh-CN`: 简体中文
- `en`: 英文
- `ja`: 日文
- `ko`: 韩文
- `ar`: 阿拉伯文
- `de`: 德文
- `fr`: 法文

## 架构特性

### 高性能内存缓存
- 使用内存缓存替代Redis，减少网络开销
- 支持高并发读写，使用读写锁保证并发安全
- 自动清理过期缓存项，防止内存泄漏

### 异步用量扣减
- 翻译请求时先检查额度，不提前扣减
- 翻译成功后立即扣减内存中的额度
- 异步批量更新数据库，减少数据库调用
- 队列容量10000，批量大小100，5秒刷新间隔
- **智能重置时间**: 优先使用订阅过期时间，无效时自动使用Apple订阅规则兜底，确保系统健壮性
- **单例模式**: AsyncQuotaService使用全局单例，避免创建多个队列和goroutine
- **完整日志记录**: 批量处理时自动记录UserAIQuotaLog，跟踪所有用量变动

### 统一用量计算
- 所有翻译服务统一使用Unicode字符数计算用量
- 移除复杂的token计算逻辑，简化计费
- 使用`utf8.RuneCountInString()`确保准确计算多语言字符

### 月度用量重置规则
按照Apple订阅系统的标准规则实现智能重置：

- **原始日期记录**: 记录用户首次订阅的日期作为重置基准
- **智能日期调整**: 当目标月份没有原始日期时，自动调整为该月最后一天
- **回归原始日期**: 当目标月份有原始日期时，自动回到原始重置日期
- **续期延迟生效**: 续期不立即增加当前周期额度，而是等到重置日才生效

**续期逻辑**:
- **当前周期存在时**：
  - 续期前：总额度10000，已使用3000，剩余7000，重置时间2024-02-15
  - 续期后：总额度10000，已使用3000，剩余7000，待生效20000，重置时间2024-02-15（保持不变），待生效重置时间2024-03-20
  - 重置时：总额度20000，已使用0，剩余20000，待生效0，重置时间2024-03-20（应用待生效额度和重置时间）
- **当前周期已结束时**：
  - 续期前：总额度5000，已使用4000，剩余1000，重置时间2024-02-15（已过期）
  - 续期后：总额度25000，已使用0，剩余25000，重置时间2024-03-20（立即生效并更新重置时间）

**重置时间管理**:
- **续期时机制**: 当前周期存在时不立即更新重置时间，使用`PendingNextResetAt`字段存储待生效的重置时间
- **重置时应用**: 重置时同时应用待生效额度和待生效重置时间，确保周期完整性
- **兜底机制**: 订阅过期时间有效时使用，无效时自动使用Apple订阅规则计算
- **异常处理**: 支持零值、负值、过期时间等异常情况的兜底处理
- **无续订处理**: 总额度为0且无待生效额度时，跳过重置操作

**重置场景**:
- 有续期：应用待生效额度，清空使用量 → 获得完整新额度
- 无续期：保持原额度，清空使用量 → 获得完整原额度
- 未订阅：总额度0，重置后仍为0

**日期示例**:
- 1月31日订阅 → 2月29日重置（2月没有31号）→ 3月31日重置（回到原始日期）
- 1月30日订阅 → 2月29日重置（2月没有30号）→ 3月30日重置（回到原始日期）
- 1月15日订阅 → 每月15日重置（所有月份都有15号）

### 智能AI翻译
- **批量翻译优先**: 多个文本合并为一次API调用，提高效率
- **严格结果解析**: 完全按照JavaScript版本逻辑实现，支持分隔符和索引号两种格式
- **自动回退机制**: 批量翻译失败时自动回退到单个翻译
- **专业提示词**: 针对批量和单个翻译使用不同的优化提示词
- **正则表达式解析**: 使用`^\[\d+\]\s*`和`^\[(\d+)\]\s*(.*)$`精确匹配索引格式

### 并发安全设计
- 内存缓存使用读写锁保证并发安全
- 用量扣减使用互斥锁防止竞态条件
- 异步队列处理，避免阻塞主流程

### 用户信息接口增强
- **下次重置时间**: 返回用户额度下次重置的时间戳（毫秒）
- **完整用量信息**: 总额度、已使用、剩余额度、重置时间一目了然
- **未订阅处理**: 未订阅用户重置时间返回0
- **实时更新**: 每次查询时自动检查并应用重置逻辑


