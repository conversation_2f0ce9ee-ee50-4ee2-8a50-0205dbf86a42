[server]
port=8080

[postgresql]
host=""
port=5432
username="bilangpage_owner"
database="bilangpage"

[jwt]
secret="O2eKWGj4LoNPcPjLC649qDeNiQpfiFtiyn57E6sczDE="
issuer="bilangpage-api"

[translation]

[translation.deepl]
api_key=""
base_url="https://api-free.deepl.com"

[translation.deepseek]
api_key=""
base_url="https://api.deepseek.com"
model="deepseek-v3"

[translation.openrouter]
api_key=""
base_url="https://openrouter.ai/api/v1"
model="gpt-4o-mini"

[redis]
host="localhost"
port=6379
password=""
db=0
